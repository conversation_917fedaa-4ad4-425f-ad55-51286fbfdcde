// frontend/src/contexts/AuthContext.tsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { User, AuthResponse, ApiResponse } from '../types';
import {
  useLoginMutation,
  useRegisterMutation,
  useLogoutMutation,
  useGetCurrentUserQuery
} from '../services/api';

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
}

interface RegisterData {
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  password: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const authReducer = (state: AuthState, action: any): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    default:
      return state;
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    token: localStorage.getItem('token'),
    isAuthenticated: false,
    loading: true,
  });

  // RTK Query hooks
  const [loginMutation] = useLoginMutation();
  const [registerMutation] = useRegisterMutation();
  const [logoutMutation] = useLogoutMutation();

  // Check if user is authenticated on mount
  const { data: currentUserData, error: currentUserError, isLoading: isCheckingAuth } = useGetCurrentUserQuery(
    undefined,
    {
      skip: !localStorage.getItem('token'),
      refetchOnMountOrArgChange: true
    }
  );

  useEffect(() => {
    if (currentUserData?.success && currentUserData.data) {
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: currentUserData.data.user,
          token: localStorage.getItem('token')
        }
      });
    } else if (currentUserError) {
      // Token is invalid, clear it
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      dispatch({ type: 'LOGOUT' });
    }

    if (!isCheckingAuth) {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [currentUserData, currentUserError, isCheckingAuth]);

  const login = async (email: string, password: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const result = await loginMutation({ email, password });

      if (result.error) {
        throw new Error(result.error.data?.error || 'Login failed');
      }

      if (result.data?.success && result.data.data) {
        const { user, tokens } = result.data.data;
        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: { user, token: tokens.access }
        });
      } else {
        throw new Error('Login failed');
      }
    } catch (error: any) {
      dispatch({ type: 'SET_LOADING', payload: false });
      throw error;
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const result = await registerMutation(userData);

      if (result.error) {
        throw new Error(result.error.data?.error || 'Registration failed');
      }

      if (result.data?.success && result.data.data) {
        const { user, tokens } = result.data.data;
        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: { user, token: tokens.access }
        });
      } else {
        throw new Error('Registration failed');
      }
    } catch (error: any) {
      dispatch({ type: 'SET_LOADING', payload: false });
      throw error;
    }
  };

  const logout = async () => {
    try {
      await logoutMutation();
    } catch (error) {
      // Even if logout API fails, we should clear local state
      console.error('Logout API failed:', error);
    } finally {
      dispatch({ type: 'LOGOUT' });
    }
  };

  return (
    <AuthContext.Provider value={{ ...state, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
