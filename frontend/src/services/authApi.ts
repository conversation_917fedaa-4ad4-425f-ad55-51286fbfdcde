// frontend/src/services/authApi.ts
import { api } from './api';
import type { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  ApiResponse 
} from '../types';

export const authApi = api.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation<ApiResponse<AuthResponse>, LoginRequest>({
      query: (credentials) => ({
        url: '/auth/login/',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<AuthResponse>) => {
        // Store tokens in localStorage on successful login
        if (response.success && response.data) {
          localStorage.setItem('token', response.data.tokens.access);
          localStorage.setItem('refreshToken', response.data.tokens.refresh);
        }
        return response;
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || { 
            success: false, 
            error: 'Login failed', 
            timestamp: new Date().toISOString() 
          }
        };
      },
    }),

    register: builder.mutation<ApiResponse<AuthResponse>, RegisterRequest>({
      query: (userData) => ({
        url: '/auth/register/',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response: ApiResponse<AuthResponse>) => {
        // Store tokens in localStorage on successful registration
        if (response.success && response.data) {
          localStorage.setItem('token', response.data.tokens.access);
          localStorage.setItem('refreshToken', response.data.tokens.refresh);
        }
        return response;
      },
      transformErrorResponse: (response: any) => {
        return {
          status: response.status,
          data: response.data || { 
            success: false, 
            error: 'Registration failed', 
            timestamp: new Date().toISOString() 
          }
        };
      },
    }),

    refreshToken: builder.mutation<ApiResponse<{ access: string }>, { refresh: string }>({
      query: (tokenData) => ({
        url: '/auth/token/refresh/',
        method: 'POST',
        body: tokenData,
      }),
      transformResponse: (response: ApiResponse<{ access: string }>) => {
        // Store new access token on successful refresh
        if (response.success && response.data) {
          localStorage.setItem('token', response.data.access);
        }
        return response;
      },
    }),

    logout: builder.mutation<void, void>({
      query: () => ({
        url: '/auth/logout/',
        method: 'POST',
      }),
      invalidatesTags: ['Auth', 'User', 'Conversation', 'Message'],
      onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
        } finally {
          // Clear tokens regardless of API response
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          
          // Clear all cached data
          dispatch(api.util.resetApiState());
        }
      },
    }),

    // Get current user profile (useful for checking auth status)
    getCurrentUser: builder.query<ApiResponse<{ user: any }>, void>({
      query: () => '/auth/me/',
      providesTags: ['Auth', 'User'],
      transformErrorResponse: (response: any) => {
        // If user is not authenticated, clear tokens
        if (response.status === 401) {
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
        }
        return {
          status: response.status,
          data: response.data || { 
            success: false, 
            error: 'Failed to get user info', 
            timestamp: new Date().toISOString() 
          }
        };
      },
    }),
  }),
  overrideExisting: false,
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useRefreshTokenMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useLazyGetCurrentUserQuery,
} = authApi;
