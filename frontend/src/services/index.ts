// frontend/src/services/index.ts
/**
 * Central export file for all API services and hooks
 * This provides a clean interface for importing RTK Query hooks throughout the app
 */

// Main API service (import this first to avoid circular dependencies)
export { api } from './api';

// Import and re-export API services after the main api is defined
// This ensures proper initialization order

// Authentication API
export {
  authApi,
  useLoginMutation,
  useRegisterMutation,
  useRefreshTokenMutation,
  useLogoutMutation,
  useGetCurrentUserQuery,
  useLazyGetCurrentUserQuery,
} from './authApi';

// Message API
export {
  messageApi,
  useGetMessagesQuery,
  useLazyGetMessagesQuery,
  useSendMessageMutation,
  useAddMessageToCacheMutation,
} from './messageApi';

// Conversation API
export {
  conversationApi,
  useGetConversationsQuery,
  useLazyGetConversationsQuery,
  useCreateConversationMutation,
  useUpdateConversationInCacheMutation,
  useUpdateConversationLastMessageMutation,
  useGetConversationQuery,
  useLazyGetConversationQuery,
} from './conversationApi';

// User API
export {
  userApi,
  useSearchUsersQuery,
  useLazySearchUsersQuery,
  useGetUserProfileQuery,
  useLazyGetUserProfileQuery,
  useGetCurrentUserProfileQuery,
  useLazyGetCurrentUserProfileQuery,
  useUpdateUserProfileMutation,
} from './userApi';

// Cache utilities
export {
  invalidateConversations,
  invalidateMessages,
  invalidateUserSearch,
  addMessageToCache,
  updateConversationInCache,
  updateConversationLastMessage,
  addOptimisticMessage,
  replaceOptimisticMessage,
  removeOptimisticMessage,
  prefetchMessages,
  prefetchConversation,
  clearAllCache,
  clearConversationCache,
} from './cacheUtils';

// Type exports
export type { Message, SendMessageRequest, FetchMessagesRequest } from './messageApi';
export type { Conversation, CreateConversationRequest } from './conversationApi';
export type { SearchUser } from './userApi';
