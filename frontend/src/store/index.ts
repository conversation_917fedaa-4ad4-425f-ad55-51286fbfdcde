// frontend/src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import messageReducer from './slices/messageSlice';
import conversationReducer from './slices/conversationSlice';

export const store = configureStore({
  reducer: {
    messages: messageReducer,
    conversations: conversationReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
