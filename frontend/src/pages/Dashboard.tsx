// frontend/src/pages/Dashboard.tsx
import React from 'react';
import { useSelector } from 'react-redux';
import { Button } from '../components/ui/Button';
import { Icon } from '../components/ui/Icon';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import type { RootState } from '../store';
import ConversationList from '../components/Chat/ConversationList';
import ChatRoom from '../components/Chat/ChatRoom';
import UserSearch from '../components/Chat/UserSearch';

export const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isConnected } = useSocket();
  const selectedConversationId = useSelector((state: RootState) => state.conversations.selectedConversationId);
  const [showUserSearch, setShowUserSearch] = React.useState(false);

  const handleLogout = () => {
    logout();
  };

  const handleConversationCreated = (conversationId: string) => {
    // The conversation will be automatically selected by the Redux action
    setShowUserSearch(false);
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Icon name="message" className="text-blue-600" size={32} />
              </div>
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900">Chat App</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Connection status */}
              <div className={`flex items-center space-x-2 text-sm ${
                isConnected ? 'text-green-600' : 'text-yellow-600'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-yellow-500 animate-pulse'
                }`}></div>
                <span>{isConnected ? 'Connected' : 'Connecting'}</span>
              </div>

              <div className="text-sm text-gray-700">
                Welcome, <span className="font-medium">{user?.firstName} {user?.lastName}</span>
              </div>
              <Button
                variant="outline"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <Icon name="log-out" size={16} />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main chat interface */}
      <div className="flex-1 flex overflow-hidden">
        {/* Conversation list sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
              <Button
                onClick={() => setShowUserSearch(true)}
                size="sm"
                className="flex items-center space-x-1"
              >
                <Icon name="plus" size={16} />
                <span>New Chat</span>
              </Button>
            </div>
          </div>
          <div className="flex-1 overflow-hidden">
            {user && <ConversationList currentUserId={user.id} />}
          </div>
        </div>

        {/* Chat area */}
        <div className="flex-1 flex flex-col">
          {selectedConversationId ? (
            <ChatRoom conversationId={selectedConversationId} />
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <Icon name="message-circle" className="mx-auto text-gray-400" size={48} />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No conversation selected</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Choose a conversation from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* User Search Modal */}
      {showUserSearch && (
        <UserSearch
          onClose={() => setShowUserSearch(false)}
          onConversationCreated={handleConversationCreated}
        />
      )}
    </div>
  );
};
